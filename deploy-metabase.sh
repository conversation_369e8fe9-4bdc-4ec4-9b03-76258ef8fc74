#!/bin/bash
# =============================================================================
# Production-grade Metabase deployment script for Google Cloud Run
# =============================================================================

# Enable strict error handling
set -e
set -o pipefail

# Function for proper error handling and cleanup
cleanup() {
    local exit_code=$?
    echo "Exiting with code $exit_code"
    # Any cleanup tasks can go here
    exit $exit_code
}

# Register the cleanup function for trapping EXIT signals
trap cleanup EXIT

# Colors for better readable output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function for formatted output
log() {
    local type=$1
    local message=$2
    local color=$NC

    case $type in
        "INFO") color=$BLUE ;;
        "SUCCESS") color=$GREEN ;;
        "WARNING") color=$YELLOW ;;
        "ERROR") color=$RED ;;
    esac

    echo -e "${color}[$type] $message${NC}"
}

# Function to check required variables
check_required_vars() {
    local missing=false

    if [ -z "$MB_DB_HOST" ]; then
        log "ERROR" "MB_DB_HOST is required but not set"
        missing=true
    fi

    if [ -z "$MB_DB_USER" ]; then
        log "ERROR" "MB_DB_USER is required but not set"
        missing=true
    fi

    if [ -z "$MB_DB_PASS" ]; then
        log "ERROR" "MB_DB_PASS is required but not set"
        missing=true
    fi

    if [ -z "$MB_DB_DBNAME" ]; then
        log "ERROR" "MB_DB_DBNAME is required but not set"
        missing=true
    fi

    if $missing; then
        log "ERROR" "Please set all required variables in .env file or export them before running this script"
        exit 1
    fi
}

# Load environment variables
if [ -f .env ]; then
    log "INFO" "Loading environment variables from .env file..."
    set -a
    source .env
    set +a
else
    log "WARNING" "No .env file found. Make sure all required variables are exported"
fi

# Verify that required variables are set
check_required_vars

# Variables with sensible defaults
PROJECT_ID=${PROJECT_ID:-"pharmaciaty-ksa-production"}
SERVICE_NAME=${SERVICE_NAME:-"metabase-service"}
REGION=${REGION:-"me-central1"}
IMAGE_NAME=${IMAGE_NAME:-"metabase"}
REPO_NAME="${IMAGE_NAME}-repo"
TIMESTAMP=$(date +%s)
NEW_IMAGE_TAG="${IMAGE_NAME}:${TIMESTAMP}"
FULL_IMAGE_PATH="$REGION-docker.pkg.dev/$PROJECT_ID/$REPO_NAME/$NEW_IMAGE_TAG"
MIN_INSTANCES=${MIN_INSTANCES:-0}
MAX_INSTANCES=${MAX_INSTANCES:-3}
IMAGES_TO_KEEP=${IMAGES_TO_KEEP:-3}

# Configure gcloud CLI and check for authentication
log "INFO" "Configuring gcloud authentication..."
if [ -n "$SERVICE_ACCOUNT" ]; then
    gcloud config set account "$SERVICE_ACCOUNT"
fi

# Verify project configuration
log "INFO" "Setting project to: $PROJECT_ID"
gcloud config set project "$PROJECT_ID"

# Enable required Google Cloud services (only if not already enabled)
log "INFO" "Ensuring required Google Cloud services are enabled..."
services_to_enable=("run.googleapis.com" "artifactregistry.googleapis.com" "cloudbuild.googleapis.com")

for service in "${services_to_enable[@]}"; do
    if ! gcloud services list --filter="name:$service" --format="value(name)" | grep -q "$service"; then
        log "INFO" "Enabling $service..."
        gcloud services enable "$service"
    else
        log "INFO" "$service is already enabled"
    fi
done

# Check and create Artifact Registry repository if not exists
log "INFO" "Checking for existing Artifact Registry repository..."
if ! gcloud artifacts repositories describe "$REPO_NAME" --location="$REGION" &> /dev/null; then
    log "INFO" "Creating Artifact Registry repository '$REPO_NAME'..."
    gcloud artifacts repositories create "$REPO_NAME" \
        --repository-format=docker \
        --location="$REGION" \
        --description="Docker Repository for Metabase"
else
    log "INFO" "Artifact Registry repository '$REPO_NAME' already exists"
fi

# Build and push Docker image using Google Cloud Build
log "INFO" "Building and pushing Docker image using Google Cloud Build..."
log "INFO" "This may take a few minutes..."
gcloud builds submit --tag "$FULL_IMAGE_PATH" . || {
    log "ERROR" "Failed to build and push the Docker image"
    exit 1
}
log "SUCCESS" "Image built and pushed to $FULL_IMAGE_PATH"

# Clean up old Docker images - more efficient approach
log "INFO" "Cleaning up old Docker images (keeping the latest $IMAGES_TO_KEEP)..."
IMAGES=$(gcloud artifacts docker images list "$REGION-docker.pkg.dev/$PROJECT_ID/$REPO_NAME" \
    --include-tags --sort-by=~CREATE_TIME --format="value(package,tags[0])")

# Skip the first IMAGES_TO_KEEP images
IMAGES_TO_DELETE=$(echo "$IMAGES" | awk -v skip="$IMAGES_TO_KEEP" 'NR>skip')

if [ -n "$IMAGES_TO_DELETE" ]; then
    echo "$IMAGES_TO_DELETE" | while read -r IMAGE TAG; do
        if [ -n "$IMAGE" ] && [ -n "$TAG" ]; then
            OLD_IMAGE="$IMAGE:$TAG"
            log "INFO" "Deleting old image: $OLD_IMAGE"
            gcloud artifacts docker images delete "$OLD_IMAGE" --quiet --async || log "WARNING" "Failed to delete image: $OLD_IMAGE"
        fi
    done
else
    log "INFO" "No old images to delete"
fi

# Deploy the service to Google Cloud Run with optimized settings
log "INFO" "Deploying to Google Cloud Run with the new image..."
gcloud run deploy "$SERVICE_NAME" \
    --image="$FULL_IMAGE_PATH" \
    --platform=managed \
    --region="$REGION" \
    --allow-unauthenticated \
    --timeout=900s \
    --memory=4Gi \
    --cpu=2 \
    --port=3000 \
    --min-instances="$MIN_INSTANCES" \
    --max-instances="$MAX_INSTANCES" \
    --set-env-vars="MB_DB_TYPE=postgres,MB_DB_HOST=${MB_DB_HOST},MB_DB_PORT=5432,MB_DB_USER=${MB_DB_USER},MB_DB_PASS=${MB_DB_PASS},MB_DB_DBNAME=${MB_DB_DBNAME},JAVA_OPTS=-Xmx3g,JAVA_TOOL_OPTIONS=-XX:+ExitOnOutOfMemoryError -XX:MaxRAMPercentage=75" \
    --service-account="${SERVICE_ACCOUNT}" \
    --session-affinity || {
        log "ERROR" "Failed to deploy service to Cloud Run"
        exit 1
    }

# Get the URL of the deployed service
SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="value(status.url)")

log "SUCCESS" "Metabase successfully deployed to Google Cloud Run!"
log "SUCCESS" "Service URL: $SERVICE_URL"

# Save detailed deployment logs in a structured format
log "INFO" "Saving deployment logs..."
LOG_FILE="deploy-log-$TIMESTAMP.json"
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=$SERVICE_NAME" \
    --limit 20 --format=json > "$LOG_FILE" || log "WARNING" "Failed to fetch logs"

log "INFO" "Deployment logs saved to $LOG_FILE"

# Display a summary of the deployment
log "INFO" "Deployment Summary:"
log "INFO" "- Project: $PROJECT_ID"
log "INFO" "- Service: $SERVICE_NAME"
log "INFO" "- Region: $REGION"
log "INFO" "- Image: $FULL_IMAGE_PATH"
log "INFO" "- Timestamp: $TIMESTAMP"
log "INFO" "- URL: $SERVICE_URL"

log "SUCCESS" "Deployment completed successfully!"
